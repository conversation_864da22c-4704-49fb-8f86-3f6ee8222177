"""
IAFFBO算法运行脚本
集成benchmark测试，支持18个客户端任务
"""

import os
import sys
import numpy as np
import time
import pickle
from datetime import datetime

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from pyIAAFBO.Tasks.benchmark import create_tasks_diff_func
from pyIAAFBO.config import get_config, validate_config, print_config
from pyIAAFBO.iaffbo_algorithm import IAFFBOAlgorithm


def run_single_experiment(config, tasks, run_id=0):
    """
    运行单次实验
    
    Args:
        config: 配置对象
        tasks: 任务列表
        run_id: 运行ID
        
    Returns:
        results: 实验结果
    """
    print(f"\n{'='*20} Run {run_id + 1} {'='*20}")
    
    # 设置随机种子
    if config.random_seed is not None:
        np.random.seed(config.random_seed + run_id)
    
    # 创建算法实例
    algorithm = IAFFBOAlgorithm(config.to_dict())
    
    # 设置任务
    algorithm.set_tasks(tasks)
    
    # 运行优化
    start_time = time.time()
    results = algorithm.run_optimization()
    end_time = time.time()

    # 保存真实评估历史为CSV
    csv_filename = algorithm.save_evaluation_history_csv(run_id=run_id + 1)

    # 添加运行信息
    results['run_id'] = run_id
    results['runtime'] = end_time - start_time
    results['config'] = config.to_dict()
    results['csv_filename'] = csv_filename

    print(f"Run {run_id + 1} completed in {end_time - start_time:.2f} seconds")

    return results


def run_multiple_experiments(config_name='default', custom_params=None, 
                           save_results=True, result_dir='results'):
    """
    运行多次实验
    
    Args:
        config_name: 配置名称
        custom_params: 自定义参数
        save_results: 是否保存结果
        result_dir: 结果保存目录
        
    Returns:
        all_results: 所有实验结果
    """
    # 获取配置
    config = get_config(config_name, custom_params)
    
    # 验证配置
    is_valid, error_messages = validate_config(config)
    if not is_valid:
        print("Configuration validation failed:")
        for msg in error_messages:
            print(f"  - {msg}")
        return None
    
    # 打印配置
    if config.verbose:
        print_config(config)
    
    # 创建任务
    print("Creating benchmark tasks...")
    tasks = create_tasks_diff_func(config.dim, normalized=False)
    
    if len(tasks) < config.client_num:
        print(f"Warning: Only {len(tasks)} tasks available, but {config.client_num} clients requested")
        print(f"Using first {config.client_num} tasks")
    
    # 确保任务数量匹配
    tasks = tasks[:config.client_num]
    
    print(f"Running {config.runs} experiments with {len(tasks)} tasks...")
    
    # 运行多次实验
    all_results = []
    total_start_time = time.time()
    
    for run_id in range(config.runs):
        try:
            results = run_single_experiment(config, tasks, run_id)
            all_results.append(results)
            
            # 打印当前最佳结果
            if config.verbose:
                best_values = results['best_values'][-1]  # 最后一轮的结果
                print(f"Final best values: {[f'{v:.6f}' for v in best_values[:3]]}...")
                
        except Exception as e:
            print(f"Error in run {run_id + 1}: {str(e)}")
            continue
    
    total_end_time = time.time()
    total_runtime = total_end_time - total_start_time
    
    print(f"\nAll experiments completed in {total_runtime:.2f} seconds")
    print(f"Average time per run: {total_runtime / len(all_results):.2f} seconds")
    
    # 保存结果
    if save_results and all_results:
        save_experiment_results(all_results, config, result_dir)
    
    return all_results


def save_experiment_results(all_results, config, result_dir='results'):
    """
    保存实验结果
    
    Args:
        all_results: 所有实验结果
        config: 配置对象
        result_dir: 结果保存目录
    """
    # 创建结果目录
    os.makedirs(result_dir, exist_ok=True)
    
    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    experiment_name = config.get_experiment_name()
    filename = f"{experiment_name}_{timestamp}.pkl"
    filepath = os.path.join(result_dir, filename)
    
    # 保存结果
    with open(filepath, 'wb') as f:
        pickle.dump({
            'results': all_results,
            'config': config.to_dict(),
            'timestamp': timestamp,
            'experiment_name': experiment_name
        }, f)
    
    print(f"Results saved to: {filepath}")
    
    # 保存统计摘要
    save_summary_statistics(all_results, config, result_dir, timestamp)


def save_summary_statistics(all_results, config, result_dir, timestamp):
    """
    保存统计摘要
    
    Args:
        all_results: 所有实验结果
        config: 配置对象
        result_dir: 结果保存目录
        timestamp: 时间戳
    """
    if not all_results:
        return
    
    # 计算统计信息
    n_runs = len(all_results)
    n_clients = config.client_num
    n_rounds = len(all_results[0]['best_values'])
    
    # 收集所有运行的最终最佳值
    final_best_values = []
    for result in all_results:
        final_values = result['best_values'][-1]  # 最后一轮
        final_best_values.append(final_values)
    
    final_best_values = np.array(final_best_values)  # shape: (n_runs, n_clients)
    
    # 计算统计量
    mean_values = np.mean(final_best_values, axis=0)
    std_values = np.std(final_best_values, axis=0)
    min_values = np.min(final_best_values, axis=0)
    max_values = np.max(final_best_values, axis=0)
    
    # 保存摘要
    summary_filename = f"{config.get_experiment_name()}_{timestamp}_summary.txt"
    summary_filepath = os.path.join(result_dir, summary_filename)
    
    with open(summary_filepath, 'w') as f:
        f.write("IAFFBO Experiment Summary\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"Experiment: {config.get_experiment_name()}\n")
        f.write(f"Timestamp: {timestamp}\n")
        f.write(f"Number of runs: {n_runs}\n")
        f.write(f"Number of clients: {n_clients}\n")
        f.write(f"Number of rounds: {n_rounds}\n\n")
        
        f.write("Configuration:\n")
        for key, value in config.to_dict().items():
            f.write(f"  {key}: {value}\n")
        f.write("\n")
        
        f.write("Final Results (across all runs):\n")
        f.write("-" * 30 + "\n")
        
        for client_id in range(n_clients):
            f.write(f"Client {client_id + 1}:\n")
            f.write(f"  Mean: {mean_values[client_id]:.6f}\n")
            f.write(f"  Std:  {std_values[client_id]:.6f}\n")
            f.write(f"  Min:  {min_values[client_id]:.6f}\n")
            f.write(f"  Max:  {max_values[client_id]:.6f}\n\n")
        
        f.write("Overall Statistics:\n")
        f.write(f"  Best overall: {np.min(final_best_values):.6f}\n")
        f.write(f"  Worst overall: {np.max(final_best_values):.6f}\n")
        f.write(f"  Mean across all: {np.mean(final_best_values):.6f}\n")
        f.write(f"  Std across all: {np.std(final_best_values):.6f}\n")
    
    print(f"Summary saved to: {summary_filepath}")


def main():
    """
    主函数
    """
    # 示例：运行默认配置
    print("Running IAFFBO with default configuration...")
    
    # 可以修改这些参数来测试不同配置
    custom_params = {
        'runs': 3,           # 减少运行次数用于测试
        'max_fe': 20,        # 减少评估次数用于测试
        'verbose': True
    }
    
    results = run_multiple_experiments(
        config_name='default',
        custom_params=custom_params,
        save_results=True,
        result_dir='results'
    )
    
    if results:
        print(f"\nExperiment completed successfully!")
        print(f"Total runs: {len(results)}")
        
        # 显示第一个客户端的收敛曲线
        if results[0]['best_values']:
            client_0_values = [round_values[0] for round_values in results[0]['best_values']]
            print(f"Client 0 convergence: {client_0_values}")
    else:
        print("Experiment failed!")


if __name__ == "__main__":
    main()
