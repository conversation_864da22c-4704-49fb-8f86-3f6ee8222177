"""
测试CSV输出功能
验证真实评估值的保存格式
"""

import os
import sys
import numpy as np
import pandas as pd

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from pyIAAFBO.Tasks.benchmark import create_tasks_diff_func
from pyIAAFBO.config import get_config
from pyIAAFBO.iaffbo_algorithm import IAFFBOAlgorithm


def test_csv_output():
    """测试CSV输出功能"""
    print("测试CSV输出功能...")
    print("=" * 50)
    
    # 创建小规模配置用于测试
    config = get_config('default', {
        'client_num': 3,        # 3个客户端
        'dim': 2,               # 2维问题
        'n_initial': 5,         # 5个初始点
        'max_fe': 8,            # 8次函数评估
        'runs': 1,              # 1次运行
        'verbose': False        # 减少输出
    })
    
    print(f"配置: {config.client_num}个客户端, {config.dim}维, {config.n_initial}初始点, {config.max_fe}次评估")
    print(f"总评估次数: {config.n_initial + config.max_fe}")
    
    # 创建任务
    tasks = create_tasks_diff_func(config.dim, normalized=False)
    tasks = tasks[:config.client_num]
    
    print(f"任务: {[str(task) for task in tasks]}")
    
    # 运行算法
    print("\n开始运行算法...")
    algorithm = IAFFBOAlgorithm(config.to_dict())
    algorithm.set_tasks(tasks)
    
    results = algorithm.run_optimization()
    
    # 保存CSV
    print("\n保存CSV文件...")
    csv_filename = algorithm.save_evaluation_history_csv(run_id=1)
    
    # 验证CSV文件
    print(f"\n验证CSV文件: {csv_filename}")
    
    if os.path.exists(csv_filename):
        df = pd.read_csv(csv_filename)
        print(f"CSV文件行数: {len(df)} (应该是 {config.n_initial + config.max_fe})")
        print(f"CSV文件列数: {len(df.columns)} (应该是 {config.client_num + 2})")
        
        print("\nCSV文件前5行:")
        print(df.head())
        
        print("\nCSV文件列名:")
        print(list(df.columns))
        
        # 检查数据完整性
        missing_data = df.isnull().sum().sum()
        if missing_data == 0:
            print("✓ 所有数据完整，无缺失值")
        else:
            print(f"⚠ 发现 {missing_data} 个缺失值")
        
        # 检查客户端数据
        client_columns = [col for col in df.columns if col.startswith('client')]
        print(f"\n客户端列: {client_columns}")
        
        for col in client_columns:
            values = df[col].dropna()
            print(f"{col}: {len(values)}个值, 范围 [{values.min():.4f}, {values.max():.4f}]")
        
        print(f"\n✓ CSV文件保存成功: {csv_filename}")
        return True
        
    else:
        print(f"✗ CSV文件未找到: {csv_filename}")
        return False


def compare_with_reference():
    """与参考文件格式对比"""
    print("\n" + "=" * 50)
    print("与参考文件格式对比")
    print("=" * 50)
    
    # 检查参考文件
    ref_file = "run1_DMT_18.18_ktp0.8_FE89_EI0.5_niid2_fedavg.csv"
    if os.path.exists(ref_file):
        print(f"参考文件: {ref_file}")
        ref_df = pd.read_csv(ref_file)
        
        print(f"参考文件格式:")
        print(f"  行数: {len(ref_df)}")
        print(f"  列数: {len(ref_df.columns)}")
        print(f"  列名: {list(ref_df.columns)}")
        print(f"  前3行:")
        print(ref_df.head(3))
        
        # 运行我们的算法生成相似格式
        config = get_config('default', {
            'client_num': 18,       # 18个客户端
            'dim': 10,              # 10维问题  
            'n_initial': 50,        # 50个初始点
            'max_fe': 60,           # 60次函数评估
            'runs': 1,
            'verbose': False
        })
        
        print(f"\n生成相似格式的文件...")
        print(f"配置: {config.client_num}个客户端, 总评估次数: {config.n_initial + config.max_fe}")
        
        # 这里只是演示，实际运行会比较耗时
        print("(实际运行需要较长时间，这里仅演示格式)")
        
        # 模拟生成文件名
        expected_filename = f"run1_IAFFBO_{config.client_num}.{config.n_initial + config.max_fe}_init{config.n_initial}_FE{config.max_fe}_LCB.csv"
        print(f"预期生成文件名: {expected_filename}")
        
    else:
        print(f"参考文件未找到: {ref_file}")


def main():
    """主函数"""
    print("IAFFBO CSV输出功能测试")
    
    # 测试CSV输出
    success = test_csv_output()
    
    # 与参考文件对比
    compare_with_reference()
    
    print("\n" + "=" * 50)
    if success:
        print("✓ CSV输出功能测试通过")
        print("\n使用说明:")
        print("1. 运行算法后会自动生成CSV文件")
        print("2. 文件名格式: run{run_id}_IAFFBO_{clients}.{total_evals}_init{init}_FE{max_fe}_{AF}.csv")
        print("3. CSV包含每次真实函数评估的目标值")
        print("4. 每行代表一次评估，每列代表一个客户端")
    else:
        print("✗ CSV输出功能测试失败")


if __name__ == "__main__":
    main()
