"""
高斯过程模型实现，基于DACE工具箱
实现与MATLAB版本完全一致的GP拟合和预测功能
"""

import numpy as np
from scipy.optimize import minimize
from scipy.linalg import cholesky, solve_triangular
from scipy.spatial.distance import pdist, squareform


class GaussianProcess:
    """
    高斯过程模型，实现DACE工具箱的功能
    """
    
    def __init__(self, regr='regpoly0', corr='corrgauss', theta0=None, 
                 lob=None, upb=None, normalize=True):
        """
        初始化GP模型
        
        Args:
            regr: 回归函数类型 ('regpoly0' 或 'regpoly1')
            corr: 相关函数类型 ('corrgauss')
            theta0: 相关参数初始值
            lob: 参数下界
            upb: 参数上界
            normalize: 是否归一化
        """
        self.regr = regr
        self.corr = corr
        self.theta0 = theta0
        self.lob = lob
        self.upb = upb
        self.normalize = normalize
        
        # 模型参数
        self.theta = None
        self.beta = None
        self.gamma = None
        self.sigma2 = None
        self.S = None
        self.Y = None
        self.C = None
        self.Ft = None
        self.G = None
        
        # 归一化参数
        self.S_mean = None
        self.S_std = None
        self.Y_mean = None
        self.Y_std = None
        
    def regpoly0(self, S):
        """常数回归函数"""
        m = S.shape[0]
        return np.ones((m, 1))
    
    def regpoly1(self, S):
        """一阶多项式回归函数"""
        m, n = S.shape
        F = np.ones((m, n + 1))
        F[:, 1:] = S
        return F
    
    def corrgauss(self, theta, d):
        """高斯相关函数"""
        theta = np.asarray(theta)
        d = np.asarray(d)
        
        if d.ndim == 1:
            d = d.reshape(1, -1)
            
        # 计算相关矩阵
        td = d**2 * theta.reshape(1, -1)
        r = np.exp(-np.sum(td, axis=1))
        
        return r
    
    def _normalize_data(self, S, Y):
        """数据归一化"""
        if self.normalize:
            self.S_mean = np.mean(S, axis=0)
            self.S_std = np.std(S, axis=0)
            self.S_std[self.S_std == 0] = 1  # 避免除零
            
            self.Y_mean = np.mean(Y)
            self.Y_std = np.std(Y)
            if self.Y_std == 0:
                self.Y_std = 1
                
            S_norm = (S - self.S_mean) / self.S_std
            Y_norm = (Y - self.Y_mean) / self.Y_std
        else:
            S_norm = S.copy()
            Y_norm = Y.copy()
            
        return S_norm, Y_norm
    
    def _denormalize_prediction(self, y_pred, mse_pred):
        """反归一化预测结果"""
        if self.normalize:
            y_pred = y_pred * self.Y_std + self.Y_mean
            mse_pred = mse_pred * (self.Y_std**2)
        return y_pred, mse_pred
    
    def _likelihood(self, theta, S, Y, F):
        """计算负对数似然函数"""
        try:
            theta = np.asarray(theta)
            m, n = S.shape
            
            # 计算距离矩阵
            distances = pdist(S, metric='euclidean')
            D = squareform(distances)
            
            # 计算相关矩阵
            R = np.zeros((m, m))
            for i in range(m):
                for j in range(m):
                    if i == j:
                        R[i, j] = 1.0
                    else:
                        d = S[i, :] - S[j, :]
                        R[i, j] = self.corrgauss(theta, d)
            
            # 添加数值稳定性
            R += np.eye(m) * 1e-10
            
            # Cholesky分解
            try:
                C = cholesky(R, lower=True)
            except np.linalg.LinAlgError:
                return 1e10
            
            # 求解线性系统
            Ft = solve_triangular(C, F, lower=True)
            Yt = solve_triangular(C, Y, lower=True)
            
            # QR分解
            Q, G = np.linalg.qr(Ft)
            
            # 计算beta
            if G.shape[0] > 0:
                beta = solve_triangular(G, Q.T @ Yt, lower=False)
            else:
                beta = np.array([np.mean(Y)])
            
            # 计算残差
            rho = Yt - Ft @ beta
            
            # 计算sigma2
            sigma2 = np.sum(rho**2) / m
            
            # 计算负对数似然
            detR = 2 * np.sum(np.log(np.diag(C)))
            nll = 0.5 * (m * np.log(sigma2) + detR)
            
            return nll
            
        except:
            return 1e10
    
    def fit(self, S, Y):
        """
        拟合GP模型
        
        Args:
            S: 输入数据 (m x n)
            Y: 输出数据 (m x 1)
        """
        S = np.asarray(S)
        Y = np.asarray(Y).flatten()
        
        if S.ndim == 1:
            S = S.reshape(-1, 1)
        
        m, n = S.shape
        
        # 数据归一化
        S_norm, Y_norm = self._normalize_data(S, Y)
        
        # 设置初始参数
        if self.theta0 is None:
            self.theta0 = 5.0 * np.ones(n)
        
        if self.lob is None:
            self.lob = 1e-5 * np.ones(n)
            
        if self.upb is None:
            self.upb = 100.0 * np.ones(n)
        
        # 回归矩阵
        if self.regr == 'regpoly0':
            F = self.regpoly0(S_norm)
        else:
            F = self.regpoly1(S_norm)
        
        # 优化超参数
        bounds = list(zip(self.lob, self.upb))
        
        result = minimize(
            self._likelihood,
            self.theta0,
            args=(S_norm, Y_norm, F),
            method='L-BFGS-B',
            bounds=bounds
        )
        
        self.theta = result.x
        
        # 使用最优参数重新计算模型参数
        self._compute_model_parameters(S_norm, Y_norm, F)
        
        return self
    
    def _compute_model_parameters(self, S, Y, F):
        """计算模型参数"""
        m, n = S.shape
        
        # 计算相关矩阵
        R = np.zeros((m, m))
        for i in range(m):
            for j in range(m):
                if i == j:
                    R[i, j] = 1.0
                else:
                    d = S[i, :] - S[j, :]
                    R[i, j] = self.corrgauss(self.theta, d)
        
        # 添加数值稳定性
        R += np.eye(m) * 1e-10
        
        # Cholesky分解
        self.C = cholesky(R, lower=True)
        
        # 求解线性系统
        self.Ft = solve_triangular(self.C, F, lower=True)
        Yt = solve_triangular(self.C, Y, lower=True)
        
        # QR分解
        Q, self.G = np.linalg.qr(self.Ft)
        
        # 计算beta
        if self.G.shape[0] > 0:
            self.beta = solve_triangular(self.G, Q.T @ Yt, lower=False)
        else:
            self.beta = np.array([np.mean(Y)])
        
        # 计算gamma
        self.gamma = solve_triangular(self.C, Y - F @ self.beta, lower=True)
        
        # 计算sigma2
        self.sigma2 = np.sum(self.gamma**2) / m
        
        # 保存训练数据
        self.S = S
        self.Y = Y
    
    def predict(self, S_new):
        """
        预测新点
        
        Args:
            S_new: 新的输入点
            
        Returns:
            y_pred: 预测均值
            mse_pred: 预测方差
        """
        S_new = np.asarray(S_new)
        if S_new.ndim == 1:
            S_new = S_new.reshape(1, -1)
        
        # 归一化新数据
        if self.normalize:
            S_new_norm = (S_new - self.S_mean) / self.S_std
        else:
            S_new_norm = S_new.copy()
        
        m_new = S_new_norm.shape[0]
        y_pred = np.zeros(m_new)
        mse_pred = np.zeros(m_new)
        
        for i in range(m_new):
            s = S_new_norm[i, :]
            
            # 计算相关向量
            r = np.zeros(self.S.shape[0])
            for j in range(self.S.shape[0]):
                d = s - self.S[j, :]
                r[j] = self.corrgauss(self.theta, d)
            
            # 回归向量
            if self.regr == 'regpoly0':
                f = self.regpoly0(s.reshape(1, -1))
            else:
                f = self.regpoly1(s.reshape(1, -1))
            
            # 预测均值
            rt = solve_triangular(self.C, r, lower=True)
            y_pred[i] = f @ self.beta + rt @ self.gamma
            
            # 预测方差
            u = solve_triangular(self.G, self.Ft.T @ rt - f.T, lower=False)
            mse_pred[i] = self.sigma2 * (1 - rt @ rt + u @ u)
            
            # 确保方差非负
            mse_pred[i] = max(mse_pred[i], 0)
        
        # 反归一化
        y_pred, mse_pred = self._denormalize_prediction(y_pred, mse_pred)
        
        return y_pred, mse_pred


def dacefit(S, Y, regr='regpoly0', corr='corrgauss', theta0=None, lob=None, upb=None):
    """
    DACE拟合函数，与MATLAB版本兼容
    
    Args:
        S: 设计点
        Y: 响应值
        regr: 回归函数
        corr: 相关函数
        theta0: 初始相关参数
        lob: 下界
        upb: 上界
    
    Returns:
        dmodel: 拟合的GP模型
    """
    gp = GaussianProcess(regr=regr, corr=corr, theta0=theta0, lob=lob, upb=upb)
    gp.fit(S, Y)
    return gp


def predictor(S_new, dmodel):
    """
    DACE预测函数，与MATLAB版本兼容
    
    Args:
        S_new: 新的设计点
        dmodel: 拟合的GP模型
    
    Returns:
        y_pred: 预测均值
        dy_pred: 预测梯度 (暂不实现)
        mse_pred: 预测方差
    """
    y_pred, mse_pred = dmodel.predict(S_new)
    dy_pred = None  # 暂不实现梯度
    
    return y_pred, dy_pred, mse_pred
