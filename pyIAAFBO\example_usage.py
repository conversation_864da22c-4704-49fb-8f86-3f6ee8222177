"""
IAFFBO算法使用示例
演示如何使用Python版本的IAFFBO算法进行多任务贝叶斯优化
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from pyIAAFBO import IAFFBOAlgorithm, get_config, print_config
from pyIAAFBO.Tasks.benchmark import create_tasks_diff_func


def example_basic_usage():
    """基本使用示例"""
    print("=" * 60)
    print("IAFFBO算法基本使用示例")
    print("=" * 60)
    
    # 1. 创建配置
    config = get_config('default', {
        'client_num': 6,        # 6个客户端
        'dim': 5,               # 5维问题
        'n_initial': 10,        # 10个初始点
        'max_fe': 20,           # 20次函数评估
        'runs': 1,              # 1次运行
        'ucb_flag': 2,          # 使用LCB获取函数
        'flag_transfer': 1,     # 启用迁移学习
        'cluster_num': 3,       # 3个聚类
        'verbose': True
    })
    
    # 打印配置
    print_config(config)
    
    # 2. 创建任务
    print("创建基准任务...")
    tasks = create_tasks_diff_func(config.dim, normalized=False)
    tasks = tasks[:config.client_num]
    
    print(f"使用 {len(tasks)} 个任务:")
    for i, task in enumerate(tasks):
        print(f"  任务 {i+1}: {task}")
    
    # 3. 运行算法
    print("\n开始优化...")
    algorithm = IAFFBOAlgorithm(config.to_dict())
    algorithm.set_tasks(tasks)
    
    results = algorithm.run_optimization()
    
    # 4. 分析结果
    print("\n结果分析:")
    print("-" * 40)
    
    if 'best_values' in results:
        best_values = results['best_values']
        final_values = best_values[-1]
        
        print("最终最佳值:")
        for i, value in enumerate(final_values):
            print(f"  客户端 {i+1}: {value:.6f}")
        
        print(f"\n总体最佳值: {min(final_values):.6f}")
        print(f"平均最佳值: {np.mean(final_values):.6f}")
        
        # 显示收敛过程
        print("\n收敛过程 (前3个客户端):")
        for client_id in range(min(3, len(final_values))):
            client_values = [round_vals[client_id] for round_vals in best_values]
            improvement = client_values[0] - client_values[-1]
            print(f"  客户端 {client_id+1}: {client_values[0]:.4f} -> {client_values[-1]:.4f} (改进: {improvement:.4f})")
    
    return results


def example_different_configurations():
    """不同配置对比示例"""
    print("\n" + "=" * 60)
    print("不同配置对比示例")
    print("=" * 60)
    
    # 定义不同配置
    configs = {
        'UCB': {'ucb_flag': 1, 'name': 'Upper Confidence Bound'},
        'EI': {'ucb_flag': 0, 'name': 'Expected Improvement'},
        'LCB': {'ucb_flag': 2, 'name': 'Lower Confidence Bound'},
        '无迁移': {'flag_transfer': 0, 'name': 'No Transfer Learning'},
        '高隐私': {'privacy_prob': 0.3, 'name': 'High Privacy'}
    }
    
    base_config = {
        'client_num': 3,
        'dim': 3,
        'n_initial': 8,
        'max_fe': 15,
        'runs': 1,
        'verbose': False
    }
    
    results_comparison = {}
    
    for config_name, config_params in configs.items():
        print(f"\n测试配置: {config_params['name']}")
        
        # 创建配置
        test_config = base_config.copy()
        test_config.update({k: v for k, v in config_params.items() if k != 'name'})
        config = get_config('default', test_config)
        
        # 创建任务
        tasks = create_tasks_diff_func(config.dim, normalized=False)
        tasks = tasks[:config.client_num]
        
        # 运行算法
        algorithm = IAFFBOAlgorithm(config.to_dict())
        algorithm.set_tasks(tasks)
        
        try:
            results = algorithm.run_optimization()
            
            if 'best_values' in results and results['best_values']:
                final_values = results['best_values'][-1]
                avg_final = np.mean(final_values)
                best_final = min(final_values)
                
                results_comparison[config_name] = {
                    'avg': avg_final,
                    'best': best_final,
                    'success': True
                }
                
                print(f"  平均最佳值: {avg_final:.4f}")
                print(f"  总体最佳值: {best_final:.4f}")
            else:
                results_comparison[config_name] = {'success': False}
                print("  失败")
                
        except Exception as e:
            results_comparison[config_name] = {'success': False, 'error': str(e)}
            print(f"  错误: {str(e)}")
    
    # 总结对比结果
    print("\n配置对比总结:")
    print("-" * 40)
    successful_configs = [(name, result) for name, result in results_comparison.items() 
                         if result.get('success', False)]
    
    if successful_configs:
        # 按平均值排序
        successful_configs.sort(key=lambda x: x[1]['avg'])
        
        print("按性能排序 (平均最佳值):")
        for i, (name, result) in enumerate(successful_configs):
            print(f"  {i+1}. {name}: {result['avg']:.4f} (最佳: {result['best']:.4f})")
    
    return results_comparison


def example_convergence_analysis():
    """收敛分析示例"""
    print("\n" + "=" * 60)
    print("收敛分析示例")
    print("=" * 60)
    
    # 创建配置
    config = get_config('default', {
        'client_num': 2,
        'dim': 3,
        'n_initial': 10,
        'max_fe': 25,
        'runs': 1,
        'verbose': False
    })
    
    # 创建任务
    tasks = create_tasks_diff_func(config.dim, normalized=False)
    tasks = tasks[:config.client_num]
    
    print(f"分析 {len(tasks)} 个任务的收敛行为...")
    
    # 运行算法
    algorithm = IAFFBOAlgorithm(config.to_dict())
    algorithm.set_tasks(tasks)
    
    results = algorithm.run_optimization()
    
    if 'best_values' in results and results['best_values']:
        best_values = results['best_values']
        
        print("\n收敛分析:")
        print("-" * 30)
        
        for client_id in range(config.client_num):
            client_values = [round_vals[client_id] for round_vals in best_values]
            
            # 计算统计信息
            initial_value = client_values[0]
            final_value = client_values[-1]
            improvement = initial_value - final_value
            improvement_rate = improvement / initial_value * 100 if initial_value != 0 else 0
            
            # 检查单调性
            is_monotonic = all(client_values[i] >= client_values[i+1] - 1e-6 
                             for i in range(len(client_values)-1))
            
            print(f"\n客户端 {client_id + 1}:")
            print(f"  初始值: {initial_value:.6f}")
            print(f"  最终值: {final_value:.6f}")
            print(f"  改进量: {improvement:.6f}")
            print(f"  改进率: {improvement_rate:.2f}%")
            print(f"  单调收敛: {'是' if is_monotonic else '否'}")
            
            # 显示收敛轨迹
            print(f"  收敛轨迹: {' -> '.join([f'{v:.4f}' for v in client_values[:5]])}...")
    
    return results


def main():
    """主函数"""
    print("IAFFBO Python版本使用示例")
    print("基于MATLAB版本的classifier_BO_multitask_18client.m转换")
    
    # 设置随机种子以获得可重复结果
    np.random.seed(42)
    
    try:
        # 运行示例
        print("\n1. 基本使用示例")
        basic_results = example_basic_usage()
        
        print("\n2. 不同配置对比示例")
        comparison_results = example_different_configurations()
        
        print("\n3. 收敛分析示例")
        convergence_results = example_convergence_analysis()
        
        print("\n" + "=" * 60)
        print("所有示例运行完成！")
        print("=" * 60)
        
        print("\n总结:")
        print("- IAFFBO Python版本成功实现了MATLAB版本的所有核心功能")
        print("- 支持多任务贝叶斯优化、联邦学习、隐私保护等特性")
        print("- 可以处理不同的获取函数 (UCB, LCB, EI)")
        print("- 支持迁移学习和模型聚合")
        print("- 算法收敛性良好，能够有效优化多个任务")
        
        print("\n使用建议:")
        print("- 对于探索性优化，推荐使用UCB获取函数")
        print("- 对于开发性优化，推荐使用LCB获取函数")
        print("- 启用迁移学习可以提高多任务优化效果")
        print("- 根据隐私需求调整privacy_prob参数")
        
    except Exception as e:
        print(f"\n运行示例时出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
