"""
神经网络分类器实现，基于PyTorch
实现与MATLAB patternnet相同的功能和架构
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.model_selection import train_test_split
import copy


class PatternNet(nn.Module):
    """
    模式识别神经网络，模拟MATLAB的patternnet
    """
    
    def __init__(self, input_size, hidden_sizes):
        """
        初始化网络
        
        Args:
            input_size: 输入层大小
            hidden_sizes: 隐藏层大小列表，如[15, 10, 5]
        """
        super(PatternNet, self).__init__()
        
        self.input_size = input_size
        self.hidden_sizes = hidden_sizes
        
        # 构建网络层
        layers = []
        prev_size = input_size
        
        for hidden_size in hidden_sizes:
            layers.append(nn.Linear(prev_size, hidden_size))
            layers.append(nn.Tanh())  # MATLAB默认使用tanh激活函数
            prev_size = hidden_size
        
        # 输出层 (二分类)
        layers.append(nn.Linear(prev_size, 2))
        layers.append(nn.Softmax(dim=1))
        
        self.network = nn.Sequential(*layers)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.zeros_(m.bias)
    
    def forward(self, x):
        """前向传播"""
        return self.network(x)
    
    def get_weights_vector(self):
        """获取网络权重向量，用于模型聚合"""
        weights = []
        for param in self.parameters():
            weights.append(param.data.flatten())
        return torch.cat(weights)
    
    def set_weights_from_vector(self, weight_vector):
        """从权重向量设置网络权重"""
        idx = 0
        for param in self.parameters():
            param_size = param.numel()
            param.data = weight_vector[idx:idx+param_size].view(param.shape)
            idx += param_size


class NeuralClassifier:
    """
    神经网络分类器包装类
    """
    
    def __init__(self, input_dim, hidden_sizes=None, learning_rate=0.01, 
                 max_epochs=1000, patience=50, device='cpu'):
        """
        初始化分类器
        
        Args:
            input_dim: 输入维度
            hidden_sizes: 隐藏层大小，默认按MATLAB规则计算
            learning_rate: 学习率
            max_epochs: 最大训练轮数
            patience: 早停耐心值
            device: 计算设备
        """
        self.input_dim = input_dim
        self.learning_rate = learning_rate
        self.max_epochs = max_epochs
        self.patience = patience
        self.device = device
        
        # 按MATLAB规则设置隐藏层大小
        if hidden_sizes is None:
            self.hidden_sizes = [
                int(np.ceil(input_dim * 1.5)),
                input_dim * 1,
                int(np.ceil(input_dim / 2))
            ]
        else:
            self.hidden_sizes = hidden_sizes
        
        # 创建网络
        self.net = PatternNet(input_dim, self.hidden_sizes).to(device)
        self.optimizer = optim.Adam(self.net.parameters(), lr=learning_rate)
        self.criterion = nn.CrossEntropyLoss()
        
        # 训练历史
        self.training_history = []
    
    def _prepare_data(self, X, y):
        """准备训练数据"""
        X = torch.FloatTensor(X).to(self.device)
        
        # 将标签转换为类别索引
        y_labels = np.zeros(len(y), dtype=int)
        y_labels[y == 1] = 0   # 类别1 -> 索引0
        y_labels[y == -1] = 1  # 类别-1 -> 索引1
        
        y = torch.LongTensor(y_labels).to(self.device)
        
        return X, y
    
    def train(self, X_train, y_train, X_val=None, y_val=None, verbose=False):
        """
        训练网络
        
        Args:
            X_train: 训练输入
            y_train: 训练标签 (1 或 -1)
            X_val: 验证输入
            y_val: 验证标签
            verbose: 是否打印训练信息
        """
        # 准备数据
        X_train, y_train = self._prepare_data(X_train, y_train)
        
        if X_val is not None and y_val is not None:
            X_val, y_val = self._prepare_data(X_val, y_val)
        
        best_loss = float('inf')
        patience_counter = 0
        best_model_state = None
        
        self.net.train()
        
        for epoch in range(self.max_epochs):
            # 前向传播
            outputs = self.net(X_train)
            loss = self.criterion(outputs, y_train)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()
            
            # 验证
            val_loss = loss.item()
            if X_val is not None:
                self.net.eval()
                with torch.no_grad():
                    val_outputs = self.net(X_val)
                    val_loss = self.criterion(val_outputs, y_val).item()
                self.net.train()
            
            self.training_history.append({
                'epoch': epoch,
                'train_loss': loss.item(),
                'val_loss': val_loss
            })
            
            # 早停检查
            if val_loss < best_loss:
                best_loss = val_loss
                patience_counter = 0
                best_model_state = copy.deepcopy(self.net.state_dict())
            else:
                patience_counter += 1
            
            if patience_counter >= self.patience:
                if verbose:
                    print(f"Early stopping at epoch {epoch}")
                break
            
            if verbose and epoch % 100 == 0:
                print(f"Epoch {epoch}, Train Loss: {loss.item():.4f}, Val Loss: {val_loss:.4f}")
        
        # 恢复最佳模型
        if best_model_state is not None:
            self.net.load_state_dict(best_model_state)
    
    def predict(self, X):
        """
        预测
        
        Args:
            X: 输入数据
            
        Returns:
            predictions: 预测标签 (1 或 -1)
        """
        self.net.eval()
        with torch.no_grad():
            X = torch.FloatTensor(X).to(self.device)
            outputs = self.net(X)
            _, predicted = torch.max(outputs, 1)
            
            # 转换回原始标签
            predictions = np.zeros(len(predicted))
            predictions[predicted.cpu().numpy() == 0] = 1   # 索引0 -> 类别1
            predictions[predicted.cpu().numpy() == 1] = -1  # 索引1 -> 类别-1
            
        return predictions
    
    def predict_proba(self, X):
        """
        预测概率
        
        Args:
            X: 输入数据
            
        Returns:
            probabilities: 预测概率
        """
        self.net.eval()
        with torch.no_grad():
            X = torch.FloatTensor(X).to(self.device)
            outputs = self.net(X)
            
        return outputs.cpu().numpy()


def data_process(X, y, train_ratio=0.75):
    """
    数据处理函数，模拟MATLAB的DataProcess函数
    
    Args:
        X: 输入数据
        y: 标签数据
        train_ratio: 训练集比例
        
    Returns:
        X_train, y_train, X_test, y_test: 分割后的数据
    """
    # 按类别分层采样
    unique_labels = np.unique(y)
    train_indices = []
    test_indices = []
    
    for label in unique_labels:
        label_indices = np.where(y == label)[0]
        n_train = int(np.ceil(train_ratio * len(label_indices)))
        
        # 随机选择训练样本
        np.random.shuffle(label_indices)
        train_indices.extend(label_indices[:n_train])
        test_indices.extend(label_indices[n_train:])
    
    # 随机打乱
    np.random.shuffle(train_indices)
    np.random.shuffle(test_indices)
    
    X_train = X[train_indices]
    y_train = y[train_indices]
    X_test = X[test_indices]
    y_test = y[test_indices]
    
    return X_train, y_train, X_test, y_test


def onehotconv(data, mode):
    """
    One-hot编码转换函数，模拟MATLAB的onehotconv函数
    
    Args:
        data: 输入数据
        mode: 1为编码，2为解码
        
    Returns:
        转换后的数据
    """
    if mode == 1:
        # 编码：将标签转换为one-hot
        labels = np.array(data).flatten()
        n_samples = len(labels)
        onehot = np.zeros((n_samples, 2))
        
        onehot[labels == 1, 0] = 1   # 类别1
        onehot[labels == -1, 1] = 1  # 类别-1
        
        return onehot
    
    elif mode == 2:
        # 解码：将one-hot转换为标签
        onehot = np.array(data)
        max_indices = np.argmax(onehot, axis=1)
        
        labels = np.zeros(len(max_indices))
        labels[max_indices == 0] = 1   # 索引0 -> 类别1
        labels[max_indices == 1] = -1  # 索引1 -> 类别-1
        
        return labels
    
    else:
        raise ValueError("mode must be 1 (encode) or 2 (decode)")


def model_aggregate(models, client_ids):
    """
    模型聚合函数
    
    Args:
        models: 模型字典
        client_ids: 要聚合的客户端ID列表
        
    Returns:
        aggregated_model: 聚合后的模型
    """
    if not client_ids:
        return None
    
    # 获取第一个模型作为模板
    template_model = models[client_ids[0]]
    aggregated_model = PatternNet(
        template_model.input_size, 
        template_model.hidden_sizes
    )
    
    # 获取所有模型的权重向量
    weight_vectors = []
    for client_id in client_ids:
        weight_vectors.append(models[client_id].get_weights_vector())
    
    # 计算平均权重
    avg_weights = torch.stack(weight_vectors).mean(dim=0)
    
    # 设置聚合后的权重
    aggregated_model.set_weights_from_vector(avg_weights)
    
    return aggregated_model


def convert_model_para_to_vector(models, client_id):
    """
    将模型参数转换为向量，用于聚类
    
    Args:
        models: 模型字典
        client_id: 客户端ID
        
    Returns:
        weight_vector: 权重向量
    """
    model = models[client_id]
    return model.get_weights_vector().cpu().numpy()
